import os
import logging
from dotenv import load_dotenv

load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_monitor.log'),
        logging.StreamHandler()
    ]
)

# 环境变量配置
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')
WECOM_WEBHOOK_URL = os.getenv('WECOM_WEBHOOK_URL', '')

# 交易所配置
SUPPORTED_EXCHANGES = {
    'binance': 'binance',      # 完全支持
    'bybit': 'bybit',          # 现货数据支持
    'okx': 'okx',              # 现货数据支持
    # 'bitget': 'bitget',      # 计划支持
    # 'mexc': 'mexc',          # 计划支持
    # 'gate': 'gate',          # 计划支持
    # 'kucoin': 'kucoin'       # 计划支持
}

# 当前完全支持的交易所（现货+期货）
FULLY_SUPPORTED_EXCHANGES = ['binance']

# 支持现货数据的交易所
SPOT_SUPPORTED_EXCHANGES = ['binance', 'bybit', 'okx']

# 告警阈值配置
SPOT_VOLUME_THRESHOLD = 50000  # 美元
PRICE_CHANGE_THRESHOLD = 2.0   # 百分比
FUTURES_OI_THRESHOLD = 5.0     # 持仓量变化百分比

# 时间间隔配置
MAIN_LOOP_INTERVAL = 60  # 秒
OI_CHECK_INTERVAL = 300  # 5分钟，用于持仓量检查

# API调用限制
API_RATE_LIMIT = 0.1  # 每次API调用间隔（秒）