#!/bin/bash
# 加密货币交易监控系统 - Linux/macOS启动脚本
# 作者: AI Assistant
# 版本: 2.0
# 日期: 2025-07-25

set -e

echo "==============================================="
echo " 加密货币交易监控系统 v2.0 (优化版)"
echo "==============================================="
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "[ERROR] Python3未安装"
    echo "请安装Python 3.7+: https://python.org/downloads/"
    exit 1
fi

echo "[INFO] Python环境检查通过"
echo ""

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "[WARNING] 未找到.env配置文件"
    echo "[INFO] 请先配置Telegram Bot:"
    echo "  1. 复制 .env.example 为 .env"
    echo "  2. 编辑 .env 文件填入真实配置"
    echo "  3. 重新运行此脚本"
    echo ""
    exit 1
fi

echo "[INFO] 配置文件检查通过"
echo ""

# 安装依赖
echo "[INFO] 检查Python依赖包..."
if pip3 install -r requirements.txt > /dev/null 2>&1; then
    echo "[INFO] 依赖包检查完成"
else
    echo "[WARNING] 依赖包安装可能有问题，继续尝试启动..."
fi
echo ""

# 运行测试
echo "[INFO] 运行系统自检..."
if python3 test_simple.py > test_result.log 2>&1; then
    echo "[INFO] 系统自检通过"
else
    echo "[WARNING] 系统自检发现问题，请查看 test_result.log"
    echo "[INFO] 继续尝试启动主程序..."
fi
echo ""

# 启动主程序
echo "[INFO] 启动监控系统..."
echo "[INFO] 使用 Ctrl+C 优雅停止程序"
echo "[INFO] 日志文件: crypto_monitor.log"
echo "==============================================="
echo ""

python3 main.py

echo ""
echo "[INFO] 监控系统已停止"