# 加密货币交易监控系统 - 环境变量配置示例
# 请复制此文件为 .env 并填入真实配置

# ==============================================
# Telegram Bot 配置 (必需)
# ==============================================
# 1. 与 @BotFather 对话创建Bot
# 2. 发送 /newbot 获取 BOT_TOKEN
# 3. 与你的Bot发消息后访问: https://api.telegram.org/bot<BOT_TOKEN>/getUpdates
# 4. 从返回结果获取 chat.id
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ==============================================
# 企业微信配置 (可选)
# ==============================================
# 1. 登录企业微信管理后台
# 2. 创建群机器人
# 3. 获取Webhook URL
WECOM_WEBHOOK_URL=your_wecom_webhook_url_here

# ==============================================
# 调试配置 (可选)
# ==============================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
# LOG_LEVEL=INFO

# API调用间隔 (秒)
# API_RATE_LIMIT=0.5

# ==============================================
# 配置示例 (请替换为真实值)
# ==============================================
# TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
# TELEGRAM_CHAT_ID=987654321
# WECOM_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
