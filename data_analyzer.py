import logging
from typing import Dict, Optional, Tuple
from datetime import datetime
from config import SPOT_VOLUME_THRESHOLD, PRICE_CHANGE_THRESHOLD, FUTURES_OI_THRESHOLD

logger = logging.getLogger(__name__)

class DataAnalyzer:
    def __init__(self):
        self.logger = logger
    
    def analyze_spot_alert(self, symbol: str, exchange_name: str, spot_data: Dict, 
                          minute_volume: float, price_change: float) -> Tuple[bool, Dict]:
        """
        分析现货放量告警条件
        
        条件：
        1. 1分钟成交额超过50,000美元
        2. 价格波动超过2%
        
        返回: (是否触发告警, 告警数据)
        """
        alert_triggered = False
        alert_data = {}
        
        try:
            # 检查成交额条件
            volume_condition = minute_volume >= SPOT_VOLUME_THRESHOLD
            
            # 检查价格波动条件
            price_condition = abs(price_change) >= PRICE_CHANGE_THRESHOLD
            
            if volume_condition and price_condition:
                alert_triggered = True
                alert_data = {
                    'type': '现货放量',
                    'symbol': symbol,
                    'exchange': exchange_name,
                    'minute_volume': minute_volume,
                    'price_change': price_change,
                    'current_price': spot_data.get('price', 0),
                    'timestamp': datetime.now(),
                    'volume_threshold': SPOT_VOLUME_THRESHOLD,
                    'price_threshold': PRICE_CHANGE_THRESHOLD
                }
                
                self.logger.info(f"现货放量告警触发 - {symbol}: 成交额=${minute_volume:.0f}, 价格变化={price_change:.2f}%")
            
            # 记录调试信息
            if volume_condition:
                self.logger.debug(f"{symbol} 成交额条件满足: ${minute_volume:.0f} >= ${SPOT_VOLUME_THRESHOLD}")
            if price_condition:
                self.logger.debug(f"{symbol} 价格波动条件满足: {price_change:.2f}% >= {PRICE_CHANGE_THRESHOLD}%")
                
        except Exception as e:
            self.logger.error(f"分析现货告警条件时出错 {symbol}: {e}")
        
        return alert_triggered, alert_data
    
    def analyze_futures_alert(self, symbol: str, futures_data: Dict, 
                            oi_change: float) -> Tuple[bool, Dict]:
        """
        分析期货加仓告警条件
        
        条件：
        5分钟内持仓量增加超过5%
        
        返回: (是否触发告警, 告警数据)
        """
        alert_triggered = False
        alert_data = {}
        
        try:
            # 检查持仓量变化条件（只关注增加的情况）
            oi_condition = oi_change >= FUTURES_OI_THRESHOLD
            
            if oi_condition:
                alert_triggered = True
                alert_data = {
                    'type': '期货加仓',
                    'symbol': symbol,
                    'oi_change': oi_change,
                    'current_oi': futures_data.get('open_interest', 0),
                    'current_price': futures_data.get('price', 0),
                    'timestamp': datetime.now(),
                    'oi_threshold': FUTURES_OI_THRESHOLD
                }
                
                self.logger.info(f"期货加仓告警触发 - {symbol}: 持仓量变化={oi_change:.2f}%")
            
            # 记录调试信息
            if oi_change > 0:
                self.logger.debug(f"{symbol} 持仓量增加: {oi_change:.2f}%")
                
        except Exception as e:
            self.logger.error(f"分析期货告警条件时出错 {symbol}: {e}")
        
        return alert_triggered, alert_data
    
    def format_alert_message(self, alert_data: Dict) -> str:
        """
        格式化告警消息
        
        格式：
        警报：[代币名称]
        类型：[现货放量/期货加仓]
        数据：[具体数据信息]
        """
        try:
            symbol = alert_data.get('symbol', '未知')
            alert_type = alert_data.get('type', '未知类型')
            timestamp = alert_data.get('timestamp', datetime.now())
            
            message = f"🚨 警报：{symbol}\n"
            message += f"📊 类型：{alert_type}\n"
            message += f"⏰ 时间：{timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            if alert_type == '现货放量':
                exchange = alert_data.get('exchange', '未知交易所')
                minute_volume = alert_data.get('minute_volume', 0)
                price_change = alert_data.get('price_change', 0)
                current_price = alert_data.get('current_price', 0)
                
                message += f"🏪 交易所：{exchange.upper()}\n"
                message += f"💰 1分钟成交额：${minute_volume:,.0f}\n"
                message += f"📈 价格波动：{price_change:+.2f}%\n"
                message += f"💲 当前价格：${current_price:.6f}\n"
                
            elif alert_type == '期货加仓':
                oi_change = alert_data.get('oi_change', 0)
                current_oi = alert_data.get('current_oi', 0)
                current_price = alert_data.get('current_price', 0)
                
                message += f"📊 持仓量变化：+{oi_change:.2f}%\n"
                message += f"🔢 当前持仓量：{current_oi:,.0f}\n"
                message += f"💲 当前价格：${current_price:.6f}\n"
            
            message += f"\n#加密货币监控 #{symbol.replace('/', '')}"
            
            return message
            
        except Exception as e:
            self.logger.error(f"格式化告警消息时出错: {e}")
            return f"告警格式化失败 - {alert_data.get('symbol', '未知')}: {alert_data.get('type', '未知类型')}"
    
    def validate_data_quality(self, symbol: str, spot_data: Optional[Dict], 
                            futures_data: Optional[Dict]) -> bool:
        """
        验证数据质量，确保数据完整性
        """
        try:
            # 检查现货数据
            if spot_data:
                price = spot_data.get('price', 0)
                volume_24h = spot_data.get('volume_24h', 0)
                if price <= 0 or volume_24h < 0:
                    self.logger.warning(f"{symbol} 现货数据质量问题: price={price}, volume={volume_24h}")
                    return False
            
            # 检查期货数据
            if futures_data:
                price = futures_data.get('price', 0)
                oi = futures_data.get('open_interest', 0)
                if price <= 0 or oi < 0:
                    self.logger.warning(f"{symbol} 期货数据质量问题: price={price}, oi={oi}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证数据质量时出错 {symbol}: {e}")
            return False
    
    def get_market_summary(self, symbol: str, spot_data: Optional[Dict], 
                          futures_data: Optional[Dict], minute_volume: float, 
                          price_change: float, oi_change: float) -> Dict:
        """
        获取市场数据摘要，用于日志记录和调试
        """
        return {
            'symbol': symbol,
            'spot_price': spot_data.get('price', 0) if spot_data else 0,
            'futures_price': futures_data.get('price', 0) if futures_data else 0,
            'minute_volume': minute_volume,
            'price_change_1m': price_change,
            'oi_change_5m': oi_change,
            'open_interest': futures_data.get('open_interest', 0) if futures_data else 0,
            'timestamp': datetime.now()
        }