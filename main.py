#!/usr/bin/env python3
"""
加密货币交易监控系统主程序 - 混合版本
使用混合数据采集器解决CCXT连接问题

作者: AI Assistant
版本: 1.1 (混合版本)
日期: 2025-01-24
"""

import time
import signal
import sys
import logging
from datetime import datetime
from typing import List
from data_collector import HybridDataCollector
from data_analyzer import DataAnalyzer
from alert_sender import AlertSender
from config import MAIN_LOOP_INTERVAL

# 配置日志
logger = logging.getLogger(__name__)

class HybridCryptoMonitor:
    def __init__(self):
        """初始化监控系统"""
        self.running = False
        self.data_collector = None
        self.data_analyzer = None
        self.alert_sender = None
        self.monitored_symbols = []
        self.loop_count = 0
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        logger.info("=== 加密货币交易监控系统启动 (混合版本) ===")
    
    def signal_handler(self, signum, frame):
        """处理退出信号"""
        logger.info(f"收到退出信号 {signum}，正在优雅关闭...")
        self.running = False
    
    def initialize_components(self) -> bool:
        """初始化所有组件"""
        try:
            logger.info("初始化系统组件...")
            
            # 初始化混合数据采集器
            logger.info("初始化混合数据采集器...")
            self.data_collector = HybridDataCollector()
            
            # 测试连接
            if not self.data_collector.test_connection():
                logger.error("数据采集器连接测试失败")
                return False
            
            # 初始化数据分析器
            logger.info("初始化数据分析器...")
            self.data_analyzer = DataAnalyzer()
            
            # 初始化告警发送器
            logger.info("初始化告警发送器...")
            self.alert_sender = AlertSender()
            
            # 获取监控的交易对列表
            logger.info("获取Binance期货交易对列表...")
            self.monitored_symbols = self.data_collector.get_binance_futures_symbols_direct()
            
            if not self.monitored_symbols:
                logger.error("未获取到任何有效的交易对")
                return False
            
            logger.info(f"成功获取 {len(self.monitored_symbols)} 个交易对")
            
            # 测试通知渠道
            logger.info("测试通知渠道连接...")
            test_results = self.alert_sender.test_connections()
            working_channels = sum(1 for result in test_results.values() if result)
            
            if working_channels == 0:
                logger.warning("所有通知渠道测试失败，告警将无法发送")
            else:
                logger.info(f"成功连接 {working_channels} 个通知渠道")
            
            # 发送系统启动消息
            if working_channels > 0:
                startup_message = f"🚀 监控系统已启动 (混合版本)\n📊 监控交易对: {len(self.monitored_symbols)} 个\n⏰ 监控间隔: {MAIN_LOOP_INTERVAL} 秒\n🔧 使用混合API采集器"
                self.alert_sender.send_system_message(startup_message)
            
            logger.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化组件时出错: {e}")
            return False
    
    def process_symbol(self, symbol: str) -> None:
        """处理单个交易对的监控逻辑"""
        try:
            logger.info(f"处理交易对: {symbol}")
            
            # 1. 找到成交额最大的现货交易所（简化版本）
            best_exchange, max_volume = self.data_collector.find_highest_volume_exchange(symbol)
            
            if max_volume == 0:
                logger.info(f"{symbol} 未找到有效的现货数据")
                return
            
            # 2. 获取现货数据
            spot_data = self.data_collector.get_spot_data_direct(symbol, best_exchange)
            if not spot_data:
                logger.info(f"{symbol} 获取 {best_exchange} 现货数据失败")
                return
            
            # 3. 获取期货数据
            futures_data = self.data_collector.get_futures_data_direct(symbol)
            if not futures_data:
                logger.info(f"{symbol} 获取Binance期货数据失败")
                return
            
            # 4. 验证数据质量
            if not self.data_analyzer.validate_data_quality(symbol, spot_data, futures_data):
                logger.info(f"{symbol} 数据质量检查失败")
                return
            
            # 5. 计算技术指标
            price_change_1m = self.data_collector.calculate_price_change(
                spot_data['price'], symbol
            )
            
            oi_change_5m = self.data_collector.calculate_oi_change(
                futures_data['open_interest'], symbol
            )
            
            minute_volume = self.data_collector.get_minute_volume(symbol, best_exchange)
            
            # 6. 更新历史数据
            historical_data = {
                'timestamp': datetime.now(),
                'price': spot_data['price'],
                'open_interest': futures_data['open_interest'],
                'volume_24h': spot_data['volume_24h'],
                'exchange': best_exchange
            }
            self.data_collector.update_historical_data(symbol, historical_data)
            
            # 7. 分析现货放量告警
            spot_alert_triggered, spot_alert_data = self.data_analyzer.analyze_spot_alert(
                symbol, best_exchange, spot_data, minute_volume, price_change_1m
            )
            
            if spot_alert_triggered:
                success = self.alert_sender.send_alert(spot_alert_data)
                if success:
                    logger.info(f"现货放量告警已发送: {symbol}")
                else:
                    logger.error(f"现货放量告警发送失败: {symbol}")
            
            # 8. 分析期货加仓告警
            futures_alert_triggered, futures_alert_data = self.data_analyzer.analyze_futures_alert(
                symbol, futures_data, oi_change_5m
            )
            
            if futures_alert_triggered:
                success = self.alert_sender.send_alert(futures_alert_data)
                if success:
                    logger.info(f"期货加仓告警已发送: {symbol}")
                else:
                    logger.error(f"期货加仓告警发送失败: {symbol}")
            
            # 9. 记录市场摘要（用于调试）
            if logger.isEnabledFor(logging.DEBUG):
                market_summary = self.data_analyzer.get_market_summary(
                    symbol, spot_data, futures_data, minute_volume, price_change_1m, oi_change_5m
                )
                logger.debug(f"市场摘要 {symbol}: {market_summary}")
            
        except Exception as e:
            logger.error(f"处理交易对 {symbol} 时出错: {e}")
    
    def run_monitoring_cycle(self) -> None:
        """执行一轮完整的监控周期"""
        try:
            self.loop_count += 1
            cycle_start_time = datetime.now()
            
            logger.info(f"开始第 {self.loop_count} 轮监控周期")
            
            processed_count = 0
            error_count = 0
            
            # 处理所有交易对
            symbols_to_process = self.monitored_symbols
            total_symbols = len(symbols_to_process)
            
            logger.info(f"开始处理 {total_symbols} 个交易对...")
            
            for i, symbol in enumerate(symbols_to_process, 1):
                if not self.running:
                    break
                
                try:
                    self.process_symbol(symbol)
                    processed_count += 1
                    
                    # 每50个交易对显示一次进度
                    if i % 50 == 0 or i == total_symbols:
                        logger.info(f"进度: {i}/{total_symbols} ({i/total_symbols*100:.1f}%) - 最新处理: {symbol}")
                        
                except Exception as e:
                    error_count += 1
                    logger.error(f"处理 {symbol} 时出错: {e}")
                
                # 短暂休眠以避免API限频
                time.sleep(0.5)
            
            # 清理过期的告警记录
            self.alert_sender.cleanup_old_alerts()
            
            cycle_duration = (datetime.now() - cycle_start_time).total_seconds()
            
            logger.info(
                f"第 {self.loop_count} 轮监控完成 - "
                f"处理: {processed_count}/{len(symbols_to_process)}, 错误: {error_count}, "
                f"耗时: {cycle_duration:.1f}秒"
            )
            
            # 每50轮发送一次系统状态报告
            if self.loop_count % 50 == 0:
                self.send_status_report()
            
        except Exception as e:
            logger.error(f"监控周期执行出错: {e}")
    
    def send_status_report(self) -> None:
        """发送系统状态报告"""
        try:
            alert_stats = self.alert_sender.get_alert_statistics()
            
            status_message = (
                f"📊 系统状态报告 (混合版本)\n"
                f"🔄 运行周期: {self.loop_count}\n"
                f"📈 监控交易对: {len(self.monitored_symbols)}\n"
                f"🚨 活跃告警: {alert_stats.get('total_active_alerts', 0)}\n"
                f"📅 近1小时告警: {alert_stats.get('recent_alerts_1h', 0)}\n"
                f"⏰ 报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            self.alert_sender.send_system_message(status_message)
            logger.info("系统状态报告已发送")
            
        except Exception as e:
            logger.error(f"发送状态报告时出错: {e}")
    
    def run(self) -> None:
        """运行主监控循环"""
        try:
            # 初始化组件
            if not self.initialize_components():
                logger.error("组件初始化失败，程序退出")
                return
            
            self.running = True
            logger.info(f"开始监控循环，间隔: {MAIN_LOOP_INTERVAL} 秒")
            
            while self.running:
                try:
                    # 执行监控周期
                    self.run_monitoring_cycle()
                    
                    if self.running:
                        logger.debug(f"等待 {MAIN_LOOP_INTERVAL} 秒...")
                        time.sleep(MAIN_LOOP_INTERVAL)
                    
                except KeyboardInterrupt:
                    logger.info("收到键盘中断信号")
                    break
                except Exception as e:
                    logger.error(f"监控循环异常: {e}")
                    logger.info(f"等待 {MAIN_LOOP_INTERVAL} 秒后重试...")
                    time.sleep(MAIN_LOOP_INTERVAL)
            
        except Exception as e:
            logger.error(f"运行监控系统时出现严重错误: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self) -> None:
        """关闭系统"""
        try:
            logger.info("正在关闭监控系统...")
            self.running = False
            
            # 发送系统关闭消息
            if self.alert_sender:
                shutdown_message = f"🛑 监控系统已关闭 (混合版本)\n⏰ 关闭时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n📊 总计运行周期: {self.loop_count}"
                self.alert_sender.send_system_message(shutdown_message)
            
            logger.info("=== 加密货币交易监控系统已关闭 ===")
            
        except Exception as e:
            logger.error(f"关闭系统时出错: {e}")

def main():
    """主函数"""
    try:
        # 创建并运行监控系统
        monitor = HybridCryptoMonitor()
        monitor.run()
        
    except Exception as e:
        logger.error(f"程序运行出现致命错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()