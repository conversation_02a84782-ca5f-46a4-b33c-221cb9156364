# 加密货币交易监控系统

## 项目概述

本项目是一个实时监控加密货币市场的自动化系统，通过分析现货和期货市场的量价关系，捕捉潜在的交易机会，并向用户发送及时的交易提醒。

## 功能特性

- 📊 **多交易所监控**: 支持Binance、Bybit、OKX、Bitget、MEXC、Gate.io、KuCoin等主流交易所
- 🚨 **智能告警**: 基于量价分析的现货放量和期货加仓告警
- 📱 **多渠道通知**: 支持Telegram Bot和企业微信Webhook通知
- ⚡ **实时监控**: 1分钟级别的实时数据监控
- 🛡️ **稳定可靠**: 完善的错误处理和自动恢复机制
- 📈 **防重复告警**: 智能冷却机制避免重复通知

## 告警规则

### 现货放量告警
- 1分钟成交额超过50,000美元
- 价格波动超过2%

### 期货加仓告警
- 5分钟内持仓量增加超过5%

## 项目结构

```
crypto-monitor/
├── main.py              # 主程序入口
├── config.py            # 配置管理
├── data_collector.py    # 数据采集模块
├── data_analyzer.py     # 数据分析模块
├── alert_sender.py      # 告警发送模块
├── test_system.py       # 完整测试套件
├── test_simple.py       # 简化测试程序
├── requirements.txt     # Python依赖
├── .env.example         # 环境变量示例
├── 需求文档.md          # 详细需求文档
└── README.md           # 项目说明
```

## 安装部署

### 1. 环境要求

- Python 3.7+
- 网络连接（用于访问交易所API）

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并填入配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# Telegram Bot 配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# 企业微信 Webhook 配置
WECOM_WEBHOOK_URL=your_wecom_webhook_url_here
```

### 4. 运行测试

```bash
# 运行简化测试
python test_simple.py

# 运行完整测试套件
python test_system.py
```

### 5. 启动系统

```bash
python main.py
```

## 配置说明

### Telegram Bot 配置

1. 创建Telegram Bot:
   - 与 @BotFather 对话
   - 发送 `/newbot` 创建新机器人
   - 获取 Bot Token

2. 获取Chat ID:
   - 与你的机器人发送消息
   - 访问 `https://api.telegram.org/bot<BOT_TOKEN>/getUpdates`
   - 从响应中获取chat id

### 企业微信Webhook配置

1. 登录企业微信管理后台
2. 创建群机器人
3. 获取Webhook URL

## 系统监控

### 日志文件

系统日志记录在 `crypto_monitor.log` 文件中，包含：
- 系统启动/停止信息
- 告警触发详情
- 错误信息
- API调用状态

### 监控指标

- **监控频率**: 每60秒一轮
- **告警冷却**: 每个交易对每种告警类型5分钟内只发送一次
- **支持交易对**: 仅监控Binance期货市场的USDT交易对

## 性能优化

- **API限频控制**: 内置限频机制避免触发交易所限制
- **内存管理**: 只保留最近10分钟的历史数据
- **并发优化**: 合理的请求间隔和批量处理

## 安全说明

- ✅ 所有敏感信息通过环境变量管理
- ✅ 不在代码中硬编码API密钥
- ✅ 完善的输入验证和错误处理
- ✅ 只读权限，不进行任何交易操作

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 确认交易所API可访问
   - 查看日志文件中的错误信息

2. **告警不发送**
   - 检查Telegram Bot Token和Chat ID
   - 验证企业微信Webhook URL
   - 运行测试程序检查配置

3. **内存使用过高**
   - 系统会自动清理10分钟前的历史数据
   - 检查是否有内存泄漏

### 日志级别

- `INFO`: 正常运行信息
- `WARNING`: 警告信息（如API调用失败）
- `ERROR`: 错误信息（如配置错误）
- `DEBUG`: 调试信息（默认关闭）

## 技术架构

### 核心模块

1. **DataCollector**: 负责从各交易所获取数据
2. **DataAnalyzer**: 分析数据并判断是否触发告警
3. **AlertSender**: 发送告警到各通知渠道
4. **CryptoMonitor**: 主控制器，协调各模块工作

### 数据流程

```
交易所API → 数据采集 → 数据分析 → 告警判断 → 消息发送
     ↓
   历史数据存储 ← 数据清理
```

## 扩展开发

### 添加新交易所

1. 在 `config.py` 的 `SUPPORTED_EXCHANGES` 中添加交易所
2. 确保CCXT库支持该交易所
3. 测试数据获取功能

### 自定义告警规则

1. 修改 `config.py` 中的阈值配置
2. 在 `DataAnalyzer` 中添加新的分析逻辑
3. 更新消息格式化函数

### 新增通知渠道

1. 在 `AlertSender` 中添加新的发送方法
2. 在配置文件中添加相关配置项
3. 更新测试代码

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题，请查看：
1. 日志文件 `crypto_monitor.log`
2. 运行测试程序诊断问题
3. 检查配置文件和环境变量

---

**免责声明**: 本系统仅提供市场数据分析和提醒功能，不构成投资建议。使用者应自行承担投资风险。