#!/usr/bin/env python3
"""
加密货币交易监控系统测试程序

对系统各个模块进行单元测试和集成测试

作者: AI Assistant
版本: 1.0
日期: 2025-01-24
"""

import os
import sys
import time
import logging
import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_collector import HybridDataCollector
from data_analyzer import DataAnalyzer
from alert_sender import AlertSender
import config

# 配置测试日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TestDataCollector(unittest.TestCase):
    """测试数据采集模块"""
    
    def setUp(self):
        """测试前准备"""
        self.collector = HybridDataCollector()
    
    def test_initialize_exchanges(self):
        """测试交易所初始化"""
        self.assertIsInstance(self.collector.exchange_endpoints, dict)
        self.assertIn('binance', self.collector.exchange_endpoints)
        self.assertIsNotNone(self.collector.binance_futures_base)
        logger.info("OK 交易所初始化测试通过")
    
    @patch('ccxt.binance')
    def test_get_binance_futures_symbols(self, mock_binance):
        """测试获取Binance期货交易对"""
        # 模拟市场数据
        mock_markets = {
            'BTC/USDT': {'type': 'future', 'quote': 'USDT'},
            'ETH/USDT': {'type': 'future', 'quote': 'USDT'},
            'BTC/BTC': {'type': 'future', 'quote': 'BTC'}  # 应该被过滤
        }
        
        # 对于HybridDataCollector，直接调用方法
        symbols = self.collector.get_binance_futures_symbols_direct()
        
        self.assertIsInstance(symbols, list)
        self.assertIn('BTC/USDT', symbols)
        self.assertIn('ETH/USDT', symbols)
        self.assertNotIn('BTC/BTC', symbols)
        logger.info("OK 获取期货交易对测试通过")
    
    def test_calculate_price_change(self):
        """测试价格变化计算"""
        symbol = 'BTC/USDT'
        current_price = 50000
        
        # 添加历史数据
        historical_data = {
            'timestamp': datetime.now() - timedelta(minutes=2),
            'price': 49000
        }
        self.collector.historical_data[symbol] = [historical_data]
        
        price_change = self.collector.calculate_price_change(current_price, symbol)
        expected_change = ((50000 - 49000) / 49000) * 100
        
        self.assertAlmostEqual(price_change, expected_change, places=2)
        logger.info("OK 价格变化计算测试通过")
    
    def test_calculate_oi_change(self):
        """测试持仓量变化计算"""
        symbol = 'BTC/USDT'
        current_oi = 105000
        
        # 添加历史数据
        historical_data = {
            'timestamp': datetime.now() - timedelta(minutes=6),
            'open_interest': 100000
        }
        self.collector.historical_data[symbol] = [historical_data]
        
        oi_change = self.collector.calculate_oi_change(current_oi, symbol)
        expected_change = ((105000 - 100000) / 100000) * 100
        
        self.assertAlmostEqual(oi_change, expected_change, places=2)
        logger.info("OK 持仓量变化计算测试通过")

class TestDataAnalyzer(unittest.TestCase):
    """测试数据分析模块"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = DataAnalyzer()
    
    def test_analyze_spot_alert(self):
        """测试现货放量告警分析"""
        symbol = 'BTC/USDT'
        exchange_name = 'binance'
        spot_data = {'price': 50000, 'volume_24h': 1000000}
        minute_volume = 60000  # 超过阈值
        price_change = 2.5  # 超过阈值
        
        triggered, alert_data = self.analyzer.analyze_spot_alert(
            symbol, exchange_name, spot_data, minute_volume, price_change
        )
        
        self.assertTrue(triggered)
        self.assertEqual(alert_data['type'], '现货放量')
        self.assertEqual(alert_data['symbol'], symbol)
        self.assertEqual(alert_data['minute_volume'], minute_volume)
        logger.info("OK 现货告警分析测试通过")
    
    def test_analyze_futures_alert(self):
        """测试期货加仓告警分析"""
        symbol = 'BTC/USDT'
        futures_data = {'price': 50000, 'open_interest': 105000}
        oi_change = 6.0  # 超过阈值
        
        triggered, alert_data = self.analyzer.analyze_futures_alert(
            symbol, futures_data, oi_change
        )
        
        self.assertTrue(triggered)
        self.assertEqual(alert_data['type'], '期货加仓')
        self.assertEqual(alert_data['symbol'], symbol)
        self.assertEqual(alert_data['oi_change'], oi_change)
        logger.info("OK 期货告警分析测试通过")
    
    def test_format_alert_message(self):
        """测试告警消息格式化"""
        alert_data = {
            'type': '现货放量',
            'symbol': 'BTC/USDT',
            'exchange': 'binance',
            'minute_volume': 60000,
            'price_change': 2.5,
            'current_price': 50000,
            'timestamp': datetime.now()
        }
        
        message = self.analyzer.format_alert_message(alert_data)
        
        self.assertIn('BTC/USDT', message)
        self.assertIn('现货放量', message)
        self.assertIn('$60,000', message)
        self.assertIn('2.50%', message)
        logger.info("OK 告警消息格式化测试通过")
    
    def test_validate_data_quality(self):
        """测试数据质量验证"""
        symbol = 'BTC/USDT'
        
        # 有效数据
        valid_spot_data = {'price': 50000, 'volume_24h': 1000000}
        valid_futures_data = {'price': 50000, 'open_interest': 100000}
        
        result = self.analyzer.validate_data_quality(symbol, valid_spot_data, valid_futures_data)
        self.assertTrue(result)
        
        # 无效数据
        invalid_spot_data = {'price': 0, 'volume_24h': -1000}
        result = self.analyzer.validate_data_quality(symbol, invalid_spot_data, valid_futures_data)
        self.assertFalse(result)
        
        logger.info("OK 数据质量验证测试通过")

class TestAlertSender(unittest.TestCase):
    """测试告警发送模块"""
    
    def setUp(self):
        """测试前准备"""
        self.sender = AlertSender()
    
    def test_should_send_alert(self):
        """测试告警冷却机制"""
        symbol = 'BTC/USDT'
        alert_type = '现货放量'
        
        # 第一次应该发送
        result1 = self.sender.should_send_alert(symbol, alert_type)
        self.assertTrue(result1)
        
        # 立即再次检查，应该被冷却
        result2 = self.sender.should_send_alert(symbol, alert_type)
        self.assertFalse(result2)
        
        logger.info("OK 告警冷却机制测试通过")
    
    @patch('requests.post')
    def test_send_telegram_message(self, mock_post):
        """测试Telegram消息发送"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # 临时修改config模块的值
        import config
        original_token = config.TELEGRAM_BOT_TOKEN
        original_chat_id = config.TELEGRAM_CHAT_ID
        
        # 设置测试值
        config.TELEGRAM_BOT_TOKEN = 'test_token'
        config.TELEGRAM_CHAT_ID = 'test_chat_id'
        
        try:
            # 重新初始化sender
            sender = AlertSender()
            
            result = sender.send_telegram_message('测试消息')
            self.assertTrue(result)
            mock_post.assert_called_once()
            
            logger.info("OK Telegram消息发送测试通过")
        finally:
            # 恢复原始配置
            config.TELEGRAM_BOT_TOKEN = original_token
            config.TELEGRAM_CHAT_ID = original_chat_id
    
    @patch('requests.post')
    def test_send_wecom_message(self, mock_post):
        """测试企业微信消息发送"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {'errcode': 0}
        mock_post.return_value = mock_response
        
        # 临时修改config模块的值
        import config
        original_webhook = config.WECOM_WEBHOOK_URL
        config.WECOM_WEBHOOK_URL = 'https://test.webhook.url'
        
        try:
            # 重新初始化sender
            sender = AlertSender()
            
            result = sender.send_wecom_message('测试消息')
            self.assertTrue(result)
            mock_post.assert_called_once()
            
            logger.info("OK 企业微信消息发送测试通过")
        finally:
            # 恢复原始配置
            config.WECOM_WEBHOOK_URL = original_webhook
    
    def test_cleanup_old_alerts(self):
        """测试清理过期告警记录"""
        # 添加一些测试数据
        old_time = datetime.now() - timedelta(minutes=20)
        recent_time = datetime.now() - timedelta(minutes=2)
        
        self.sender.last_alerts = {
            'BTC/USDT_现货放量': old_time,
            'ETH/USDT_期货加仓': recent_time
        }
        
        self.sender.cleanup_old_alerts()
        
        # 检查过期记录是否被清理
        self.assertNotIn('BTC/USDT_现货放量', self.sender.last_alerts)
        self.assertIn('ETH/USDT_期货加仓', self.sender.last_alerts)
        
        logger.info("OK 清理过期告警记录测试通过")

class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def test_system_workflow(self):
        """测试完整的系统工作流程"""
        logger.info("开始系统集成测试...")
        
        # 初始化组件
        collector = HybridDataCollector()
        analyzer = DataAnalyzer()
        sender = AlertSender()
        
        # 检查组件初始化
        self.assertIsNotNone(collector)
        self.assertIsNotNone(analyzer)
        self.assertIsNotNone(sender)
        
        # 测试数据流
        symbol = 'BTC/USDT'
        
        # 模拟数据
        spot_data = {'price': 50000, 'volume_24h': 2000000}
        futures_data = {'price': 50000, 'open_interest': 100000}
        
        # 验证数据质量
        is_valid = analyzer.validate_data_quality(symbol, spot_data, futures_data)
        self.assertTrue(is_valid)
        
        # 测试告警逻辑（使用高阈值确保不触发）
        minute_volume = 1000  # 低于阈值
        price_change = 1.0    # 低于阈值
        oi_change = 1.0       # 低于阈值
        
        spot_triggered, _ = analyzer.analyze_spot_alert(
            symbol, 'binance', spot_data, minute_volume, price_change
        )
        
        futures_triggered, _ = analyzer.analyze_futures_alert(
            symbol, futures_data, oi_change
        )
        
        # 应该都不触发
        self.assertFalse(spot_triggered)
        self.assertFalse(futures_triggered)
        
        logger.info("OK 系统集成测试通过")

def run_performance_test():
    """运行性能测试"""
    logger.info("开始性能测试...")
    
    collector = HybridDataCollector()
    analyzer = DataAnalyzer()
    
    # 模拟处理100个交易对
    symbols = [f'SYMBOL{i}/USDT' for i in range(100)]
    
    start_time = time.time()
    
    for symbol in symbols:
        # 模拟数据处理
        spot_data = {'price': 1000, 'volume_24h': 1000000}
        futures_data = {'price': 1000, 'open_interest': 50000}
        
        # 验证数据
        analyzer.validate_data_quality(symbol, spot_data, futures_data)
        
        # 分析告警
        analyzer.analyze_spot_alert(symbol, 'binance', spot_data, 1000, 0.5)
        analyzer.analyze_futures_alert(symbol, futures_data, 1.0)
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"性能测试完成: 处理 {len(symbols)} 个交易对耗时 {duration:.2f} 秒")
    logger.info(f"平均每个交易对处理时间: {duration/len(symbols)*1000:.2f} 毫秒")
    
    # 性能要求：每个交易对处理时间应小于100毫秒
    avg_time_per_symbol = duration / len(symbols)
    assert avg_time_per_symbol < 0.1, f"性能测试失败：平均处理时间 {avg_time_per_symbol:.3f}s > 0.1s"
    
    logger.info("OK 性能测试通过")

def run_mock_monitoring_cycle():
    """运行模拟监控周期"""
    logger.info("开始模拟监控周期测试...")
    
    try:
        # 创建模拟组件
        collector = HybridDataCollector()
        analyzer = DataAnalyzer()
        sender = AlertSender()
        
        # 模拟几个交易对
        test_symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        
        for symbol in test_symbols:
            logger.info(f"模拟处理: {symbol}")
            
            # 模拟现货数据
            spot_data = {
                'price': 50000 if 'BTC' in symbol else 3000,
                'volume_24h': 2000000,
                'timestamp': datetime.now()
            }
            
            # 模拟期货数据
            futures_data = {
                'price': spot_data['price'],
                'open_interest': 100000,
                'timestamp': datetime.now()
            }
            
            # 验证数据
            if analyzer.validate_data_quality(symbol, spot_data, futures_data):
                logger.info(f"  OK {symbol} 数据质量验证通过")
            
            # 模拟分析（使用不会触发告警的值）
            minute_volume = 1000
            price_change = 0.5
            oi_change = 1.0
            
            # 分析现货告警
            spot_triggered, spot_data_result = analyzer.analyze_spot_alert(
                symbol, 'binance', spot_data, minute_volume, price_change
            )
            
            # 分析期货告警
            futures_triggered, futures_data_result = analyzer.analyze_futures_alert(
                symbol, futures_data, oi_change
            )
            
            logger.info(f"  OK {symbol} 告警分析完成 (现货: {spot_triggered}, 期货: {futures_triggered})")
            
            # 更新历史数据
            historical_data = {
                'timestamp': datetime.now(),
                'price': spot_data['price'],
                'open_interest': futures_data['open_interest'],
                'volume_24h': spot_data['volume_24h'],
                'exchange': 'binance'
            }
            collector.update_historical_data(symbol, historical_data)
        
        logger.info("OK 模拟监控周期测试完成")
        return True
        
    except Exception as e:
        logger.error(f"模拟监控周期测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=== 开始加密货币交易监控系统测试 ===")
    
    try:
        # 1. 运行单元测试
        logger.info("\n1. 运行单元测试...")
        test_suite = unittest.TestLoader().loadTestsFromModule(sys.modules[__name__])
        test_runner = unittest.TextTestRunner(verbosity=2)
        test_result = test_runner.run(test_suite)
        
        if not test_result.wasSuccessful():
            logger.error("单元测试失败")
            return False
        
        logger.info("OK 所有单元测试通过")
        
        # 2. 运行性能测试
        logger.info("\n2. 运行性能测试...")
        run_performance_test()
        
        # 3. 运行模拟监控周期
        logger.info("\n3. 运行模拟监控周期...")
        if not run_mock_monitoring_cycle():
            return False
        
        # 4. 测试配置完整性
        logger.info("\n4. 检查配置完整性...")
        
        required_configs = [
            'SUPPORTED_EXCHANGES',
            'SPOT_VOLUME_THRESHOLD',
            'PRICE_CHANGE_THRESHOLD',
            'FUTURES_OI_THRESHOLD',
            'MAIN_LOOP_INTERVAL'
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                logger.info(f"  OK {config_name}: {getattr(config, config_name)}")
            else:
                logger.error(f"  ERROR 缺少配置: {config_name}")
                return False
        
        logger.info("OK 配置完整性检查通过")
        
        # 5. 输出测试总结
        logger.info("\n=== 测试总结 ===")
        logger.info("OK 单元测试: 通过")
        logger.info("OK 性能测试: 通过")
        logger.info("OK 集成测试: 通过")
        logger.info("OK 配置检查: 通过")
        logger.info("OK 所有测试完成，系统可以投入使用")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)