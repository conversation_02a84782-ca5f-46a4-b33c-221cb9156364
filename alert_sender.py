import requests
import logging
import time
from typing import Dict, List
from datetime import datetime, timedelta
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, WECOM_WEBHOOK_URL

logger = logging.getLogger(__name__)

class AlertSender:
    def __init__(self):
        self.logger = logger
        self.last_alerts = {}  # 用于防止重复告警
        self.alert_cooldown = 300  # 5分钟冷却期（秒）
        
        # 验证配置
        self.telegram_enabled = bool(TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID)
        self.wecom_enabled = bool(WECOM_WEBHOOK_URL)
        
        if not self.telegram_enabled and not self.wecom_enabled:
            self.logger.warning("未配置任何通知渠道，告警将无法发送")
        else:
            enabled_channels = []
            if self.telegram_enabled:
                enabled_channels.append("Telegram")
            if self.wecom_enabled:
                enabled_channels.append("企业微信")
            self.logger.info(f"已启用通知渠道: {', '.join(enabled_channels)}")
    
    def should_send_alert(self, symbol: str, alert_type: str) -> bool:
        """
        检查是否应该发送告警（避免重复告警）
        
        Args:
            symbol: 交易对符号
            alert_type: 告警类型
            
        Returns:
            bool: 是否应该发送告警
        """
        try:
            alert_key = f"{symbol}_{alert_type}"
            current_time = datetime.now()
            
            if alert_key in self.last_alerts:
                last_alert_time = self.last_alerts[alert_key]
                time_diff = (current_time - last_alert_time).total_seconds()
                
                if time_diff < self.alert_cooldown:
                    self.logger.debug(f"告警冷却中: {alert_key}, 剩余时间: {self.alert_cooldown - time_diff:.0f}秒")
                    return False
            
            # 更新最后告警时间
            self.last_alerts[alert_key] = current_time
            return True
            
        except Exception as e:
            self.logger.error(f"检查告警冷却时出错: {e}")
            return True  # 出错时允许发送
    
    def send_telegram_message(self, message: str) -> bool:
        """
        发送Telegram消息
        
        Args:
            message: 要发送的消息内容
            
        Returns:
            bool: 发送是否成功
        """
        if not self.telegram_enabled:
            self.logger.debug("Telegram未启用，跳过发送")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
            payload = {
                'chat_id': TELEGRAM_CHAT_ID,
                'text': message,
                'parse_mode': 'HTML',
                'disable_web_page_preview': True
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            self.logger.info("Telegram消息发送成功")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"发送Telegram消息失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"发送Telegram消息时出现未知错误: {e}")
            return False
    
    def send_wecom_message(self, message: str) -> bool:
        """
        发送企业微信消息
        
        Args:
            message: 要发送的消息内容
            
        Returns:
            bool: 发送是否成功
        """
        if not self.wecom_enabled:
            self.logger.debug("企业微信未启用，跳过发送")
            return False
        
        try:
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(WECOM_WEBHOOK_URL, json=payload, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                self.logger.info("企业微信消息发送成功")
                return True
            else:
                self.logger.error(f"企业微信消息发送失败: {result}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"发送企业微信消息失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"发送企业微信消息时出现未知错误: {e}")
            return False
    
    def send_alert(self, alert_data: Dict) -> bool:
        """
        发送告警到所有启用的渠道
        
        Args:
            alert_data: 告警数据字典
            
        Returns:
            bool: 是否至少有一个渠道发送成功
        """
        try:
            symbol = alert_data.get('symbol', '未知')
            alert_type = alert_data.get('type', '未知类型')
            
            # 检查是否应该发送告警（防重复）
            if not self.should_send_alert(symbol, alert_type):
                return False
            
            # 从data_analyzer导入消息格式化函数
            from data_analyzer import DataAnalyzer
            analyzer = DataAnalyzer()
            message = analyzer.format_alert_message(alert_data)
            
            success_count = 0
            total_channels = 0
            
            # 发送到Telegram
            if self.telegram_enabled:
                total_channels += 1
                if self.send_telegram_message(message):
                    success_count += 1
                time.sleep(0.5)  # 短暂延迟避免频率限制
            
            # 发送到企业微信
            if self.wecom_enabled:
                total_channels += 1
                if self.send_wecom_message(message):
                    success_count += 1
                time.sleep(0.5)  # 短暂延迟
            
            if success_count > 0:
                self.logger.info(f"告警发送完成: {symbol} - {alert_type} ({success_count}/{total_channels} 渠道成功)")
                return True
            else:
                self.logger.error(f"所有渠道告警发送失败: {symbol} - {alert_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送告警时出错: {e}")
            return False
    
    def send_system_message(self, message: str) -> bool:
        """
        发送系统消息（如启动、错误等）
        
        Args:
            message: 系统消息内容
            
        Returns:
            bool: 是否发送成功
        """
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            formatted_message = f"🤖 系统消息\n⏰ 时间: {timestamp}\n📝 内容: {message}"
            
            success = False
            
            if self.telegram_enabled:
                if self.send_telegram_message(formatted_message):
                    success = True
            
            if self.wecom_enabled:
                if self.send_wecom_message(formatted_message):
                    success = True
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送系统消息时出错: {e}")
            return False
    
    def test_connections(self) -> Dict[str, bool]:
        """
        测试所有通知渠道的连接
        
        Returns:
            Dict[str, bool]: 各渠道的连接状态
        """
        results = {}
        test_message = f"🧪 连接测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if self.telegram_enabled:
            results['telegram'] = self.send_telegram_message(test_message)
        else:
            results['telegram'] = False
            
        if self.wecom_enabled:
            results['wecom'] = self.send_wecom_message(test_message)
        else:
            results['wecom'] = False
        
        self.logger.info(f"通知渠道测试结果: {results}")
        return results
    
    def cleanup_old_alerts(self):
        """
        清理过期的告警记录，释放内存
        """
        try:
            current_time = datetime.now()
            expired_keys = []
            
            for alert_key, alert_time in self.last_alerts.items():
                if (current_time - alert_time).total_seconds() > self.alert_cooldown * 2:
                    expired_keys.append(alert_key)
            
            for key in expired_keys:
                del self.last_alerts[key]
            
            if expired_keys:
                self.logger.debug(f"清理了 {len(expired_keys)} 个过期告警记录")
                
        except Exception as e:
            self.logger.error(f"清理告警记录时出错: {e}")
    
    def get_alert_statistics(self) -> Dict:
        """
        获取告警统计信息
        
        Returns:
            Dict: 告警统计数据
        """
        try:
            current_time = datetime.now()
            active_alerts = len(self.last_alerts)
            
            # 计算最近1小时的告警数量
            one_hour_ago = current_time - timedelta(hours=1)
            recent_alerts = sum(1 for alert_time in self.last_alerts.values() 
                              if alert_time > one_hour_ago)
            
            return {
                'total_active_alerts': active_alerts,
                'recent_alerts_1h': recent_alerts,
                'telegram_enabled': self.telegram_enabled,
                'wecom_enabled': self.wecom_enabled,
                'cooldown_period': self.alert_cooldown
            }
            
        except Exception as e:
            self.logger.error(f"获取告警统计时出错: {e}")
            return {}