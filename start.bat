@echo off
REM 加密货币交易监控系统 - Windows启动脚本
REM 作者: AI Assistant
REM 版本: 2.0
REM 日期: 2025-07-25

echo ===============================================
echo  加密货币交易监控系统 v2.0 (优化版)
echo ===============================================
echo.

REM 检查Python环境
python --version > nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    echo 请安装Python 3.7+: https://python.org/downloads/
    pause
    exit /b 1
)

echo [INFO] Python环境检查通过
echo.

REM 检查配置文件
if not exist ".env" (
    echo [WARNING] 未找到.env配置文件
    echo [INFO] 请先配置Telegram Bot:
    echo   1. 复制 .env.example 为 .env
    echo   2. 编辑 .env 文件填入真实配置
    echo   3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo [INFO] 配置文件检查通过
echo.

REM 安装依赖
echo [INFO] 检查Python依赖包...
pip install -r requirements.txt > nul 2>&1
if errorlevel 1 (
    echo [WARNING] 依赖包安装可能有问题，继续尝试启动...
) else (
    echo [INFO] 依赖包检查完成
)
echo.

REM 运行测试
echo [INFO] 运行系统自检...
python test_simple.py > test_result.log 2>&1
if errorlevel 1 (
    echo [WARNING] 系统自检发现问题，请查看 test_result.log
    echo [INFO] 继续尝试启动主程序...
) else (
    echo [INFO] 系统自检通过
)
echo.

REM 启动主程序
echo [INFO] 启动监控系统...
echo [INFO] 使用 Ctrl+C 优雅停止程序
echo [INFO] 日志文件: crypto_monitor.log
echo ===============================================
echo.

python main.py
echo.
echo [INFO] 监控系统已停止
pause