# 加密货币交易监控系统 - 软件需求规格说明书

## 1. 文档信息

- **项目名称**：加密货币交易监控系统
- **版本**：1.0
- **编制日期**：2025-01-24
- **文档状态**：草案

## 2. 项目概述

### 2.1 项目背景

本项目旨在开发一个实时监控加密货币市场的自动化系统，通过分析现货和期货市场的量价关系，捕捉潜在的交易机会，并向用户发送及时的交易提醒。

### 2.2 项目目标

- 实现对Binance期货市场及多家交易所现货市场的实时监控
- 通过量价分析识别短线交易机会
- 提供多渠道（Telegram、企业微信）的实时告警通知
- 支持不同市场环境下的策略适配

### 2.3 适用场景

1. **牛市环境**：通过捕捉量价齐升的爆发信号，精准识别潜力品种
2. **熊市环境**：仅凭价格动态变化，发掘潜在投资机会

## 3. 系统架构设计

### 3.1 核心策略

- 监控Binance期货市场作为主力资金和做市商的主要阵地
- 跟踪多家交易所的现货价格作为指数价格操控的关键指标
- 通过持仓量变化结合价格波动识别交易机会

### 3.2 技术架构

- **开发语言**：Python 3.x
- **核心库**：CCXT（统一交易所API接口库）
- **通知服务**：Telegram Bot API、企业微信Webhook
- **运行模式**：无限循环脚本，定时执行

## 4. 功能需求

### 4.1 数据采集功能

#### 4.1.1 交易所接入
- 支持以下交易所的数据接入：
  - Binance（期货和现货）
  - Bybit
  - OKX
  - Bitget
  - MEXC
  - Gate.io
  - KuCoin

#### 4.1.2 监控范围
- 仅监控在Binance有期货交易对的代币
- 仅监控USDT计价的交易对（如BTC/USDT）

#### 4.1.3 数据采集内容
- 现货市场：价格、成交量、24小时成交额
- 期货市场：持仓量、价格变化

### 4.2 数据分析功能

#### 4.2.1 现货交易所筛选
- 对每个代币，动态识别当日成交额最大的现货交易所
- 将该交易所作为该代币的现货数据源

#### 4.2.2 告警触发条件

**现货放量告警**：
- 1分钟成交额超过50,000美元
- 且价格波动超过2%（价格波动 = (当前价格 - 1分钟前价格) / 1分钟前价格 * 100）

**期货加仓告警**：
- 5分钟内持仓量增加超过5%（持仓增加 = (当前持仓 - 5分钟前持仓) / 5分钟前持仓 * 100）

### 4.3 通知功能

#### 4.3.1 通知渠道
- Telegram Bot
- 企业微信Webhook

#### 4.3.2 消息格式
```
警报：[代币名称]
类型：[现货放量/期货加仓]
数据：[具体数据信息]
```

示例：
```
警报：BTC/USDT
类型：现货放量
数据：1分钟成交额: $60,000, 价格波动: 2.5%
```

## 5. 技术需求

### 5.1 开发环境
- Python 3.x
- 依赖库：
  - ccxt
  - requests
  - logging
  - time
  - os
  - datetime

### 5.2 配置管理
通过环境变量管理敏感信息：
- `TELEGRAM_BOT_TOKEN`：Telegram机器人令牌
- `TELEGRAM_CHAT_ID`：Telegram聊天ID
- `WECOM_WEBHOOK_URL`：企业微信Webhook URL

### 5.3 数据存储
- 使用内存数据结构（字典或DataFrame）存储最近5分钟的历史数据
- 包含每个代币的持仓量和价格信息

### 5.4 程序结构要求

1. **模块化设计**
   - 数据采集模块
   - 数据分析模块
   - 告警发送模块
   - 日志记录模块

2. **主程序流程**
   ```python
   1. 初始化配置和交易所实例
   2. 获取Binance期货代币列表
   3. 进入主循环：
      a. 遍历每个代币
      b. 识别最大现货交易所
      c. 获取现货和期货数据
      d. 分析是否触发告警条件
      e. 发送告警（如需要）
      f. 更新历史数据
   4. 休眠1分钟
   5. 返回步骤3
   ```

## 6. 性能需求

### 6.1 运行频率
- 主循环执行间隔：1分钟
- 使用`time.sleep(60)`控制执行频率

### 6.2 API调用优化
- 实施速率限制以避免触发交易所API限制
- 批量获取数据以减少API调用次数
- 合理使用缓存机制

### 6.3 异常处理
- 捕获并处理所有API调用异常
- 处理网络连接错误
- 处理API限频错误
- 所有异常需记录到日志

## 7. 非功能性需求

### 7.1 可靠性
- 程序需24/7稳定运行
- 异常发生时自动恢复
- 关键操作失败时进行重试

### 7.2 可维护性
- 代码需包含详细注释
- 使用有意义的变量和函数命名
- 遵循Python PEP 8编码规范

### 7.3 日志记录
- 使用Python logging模块
- 记录级别：INFO及以上
- 记录内容：
  - 程序启动/停止
  - 告警触发详情
  - 异常错误信息
  - API调用状态

### 7.4 安全性
- API密钥不得硬编码在代码中
- 使用环境变量管理敏感信息
- 实施输入验证和错误处理

## 8. 优化建议

### 8.1 降噪优化
基于用户反馈，建议：
- 优先使用价格波动作为主要过滤条件
- 成交量作为辅助参考指标
- 可根据市场状况动态调整阈值

### 8.2 扩展性考虑
- 预留参数配置接口，支持动态调整告警阈值
- 支持添加新的交易所和交易对
- 支持自定义告警规则

## 9. 交付物

1. 完整的Python源代码
2. 部署和配置说明文档
3. API使用说明
4. 测试报告

## 10. 验收标准

1. 系统能够成功连接所有指定交易所
2. 能够准确识别符合条件的交易机会
3. 告警通知能够及时发送到指定渠道
4. 程序能够稳定运行24小时以上
5. 日志记录完整且格式规范

## 11. 附录

### 11.1 API接口说明

**Telegram发送消息**：
```
POST https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage
参数：
- chat_id: TELEGRAM_CHAT_ID
- text: 消息内容
```

**企业微信发送消息**：
```
POST {WECOM_WEBHOOK_URL}
JSON Body:
{
    "msgtype": "text",
    "text": {
        "content": "消息内容"
    }
}
```

### 11.2 代码示例结构
```python
import ccxt
import time
import logging
import os
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)

# 获取环境变量
bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
# ... 其他配置

# 初始化交易所
exchanges = {
    'binance': ccxt.binance(),
    # ... 其他交易所
}

# 主循环
while True:
    # 监控逻辑
    time.sleep(60)
```