#!/usr/bin/env python3
"""
混合数据采集器 - 结合直接API调用和CCXT
当CCXT连接有问题时，使用直接HTTP请求作为备用方案
"""

import requests
import time
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class HybridDataCollector:
    def __init__(self):
        self.logger = logger
        self.session = requests.Session()
        self.session.timeout = 10
        self.historical_data = {}
        
        # Binance API端点
        self.binance_spot_base = "https://api.binance.com/api/v3"
        self.binance_futures_base = "https://fapi.binance.com/fapi/v1"
        
        # 支持的交易所API端点
        self.exchange_endpoints = {
            'binance': 'https://api.binance.com/api/v3',
            'bybit': 'https://api.bybit.com/v5',
            'okx': 'https://www.okx.com/api/v5',
            'gate': 'https://api.gateio.ws/api/v4',
        }
    
    def get_binance_futures_symbols_direct(self) -> List[str]:
        """直接通过HTTP API获取Binance期货交易对"""
        try:
            url = f"{self.binance_futures_base}/exchangeInfo"
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                symbols = []
                
                for symbol_info in data.get('symbols', []):
                    symbol = symbol_info.get('symbol', '')
                    status = symbol_info.get('status', '')
                    
                    if symbol.endswith('USDT') and status == 'TRADING':
                        # 转换为CCXT格式
                        formatted_symbol = f"{symbol[:-4]}/USDT"
                        symbols.append(formatted_symbol)
                
                logger.info(f"直接API获取到 {len(symbols)} 个Binance期货USDT交易对")
                return symbols
            else:
                logger.error(f"获取期货交易对失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取Binance期货交易对失败: {e}")
            return []
    
    def get_spot_data_direct(self, symbol: str, exchange_name: str) -> Optional[Dict]:
        """直接通过HTTP API获取现货数据"""
        try:
            if exchange_name == 'binance':
                return self._get_binance_spot_data(symbol)
            elif exchange_name == 'bybit':
                return self._get_bybit_spot_data(symbol)
            elif exchange_name == 'okx':
                return self._get_okx_spot_data(symbol)
            else:
                logger.info(f"暂不支持 {exchange_name} 交易所")
                return None
                
        except Exception as e:
            logger.info(f"获取 {exchange_name} {symbol} 现货数据失败: {e}")
            return None
    
    def _get_binance_spot_data(self, symbol: str) -> Optional[Dict]:
        """获取Binance现货数据"""
        try:
            binance_symbol = symbol.replace('/', '')
            url = f"{self.binance_spot_base}/ticker/24hr"
            params = {'symbol': binance_symbol}
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'price': float(data['lastPrice']),
                    'volume_24h': float(data['quoteVolume']),
                    'timestamp': datetime.now()
                }
            return None
        except Exception as e:
            logger.info(f"获取Binance现货数据失败: {e}")
            return None
    
    def _get_bybit_spot_data(self, symbol: str) -> Optional[Dict]:
        """获取Bybit现货数据"""
        try:
            # Bybit使用不同的符号格式
            bybit_symbol = symbol.replace('/', '')
            url = f"{self.exchange_endpoints['bybit']}/market/tickers"
            params = {'category': 'spot', 'symbol': bybit_symbol}
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                    ticker = data['result']['list'][0]
                    return {
                        'price': float(ticker['lastPrice']),
                        'volume_24h': float(ticker['turnover24h']),
                        'timestamp': datetime.now()
                    }
            return None
        except Exception as e:
            logger.info(f"获取Bybit现货数据失败: {e}")
            return None
    
    def _get_okx_spot_data(self, symbol: str) -> Optional[Dict]:
        """获取OKX现货数据"""
        try:
            # OKX使用横杠分隔符
            okx_symbol = symbol.replace('/', '-')
            url = f"{self.exchange_endpoints['okx']}/market/ticker"
            params = {'instId': okx_symbol}
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '0' and data.get('data'):
                    ticker = data['data'][0]
                    return {
                        'price': float(ticker['last']),
                        'volume_24h': float(ticker['volCcy24h']),
                        'timestamp': datetime.now()
                    }
            return None
        except Exception as e:
            logger.info(f"获取OKX现货数据失败: {e}")
            return None
    
    def get_futures_data_direct(self, symbol: str) -> Optional[Dict]:
        """直接通过HTTP API获取Binance期货数据"""
        try:
            # 转换符号格式
            binance_symbol = symbol.replace('/', '')
            
            # 获取价格数据
            ticker_url = f"{self.binance_futures_base}/ticker/24hr"
            ticker_params = {'symbol': binance_symbol}
            
            ticker_response = self.session.get(ticker_url, params=ticker_params, timeout=10)
            
            if ticker_response.status_code != 200:
                return None
            
            ticker_data = ticker_response.json()
            
            # 获取持仓量数据
            oi_url = f"{self.binance_futures_base}/openInterest"
            oi_params = {'symbol': binance_symbol}
            
            oi_response = self.session.get(oi_url, params=oi_params, timeout=10)
            
            if oi_response.status_code != 200:
                return None
            
            oi_data = oi_response.json()
            
            return {
                'price': float(ticker_data['lastPrice']),
                'open_interest': float(oi_data['openInterest']),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.info(f"获取Binance期货 {symbol} 数据失败: {e}")
            return None
    
    def find_highest_volume_exchange(self, symbol: str) -> Tuple[str, float]:
        """找到指定代币成交额最大的现货交易所"""
        exchanges_to_check = ['binance', 'bybit', 'okx']
        best_exchange = 'binance'
        max_volume = 0
        
        for exchange in exchanges_to_check:
            try:
                data = self.get_spot_data_direct(symbol, exchange)
                if data and data['volume_24h'] > max_volume:
                    max_volume = data['volume_24h']
                    best_exchange = exchange
                    logger.debug(f"{symbol} 在 {exchange} 的成交额: ${data['volume_24h']:,.0f}")
                # 短暂休眠避免API限频
                time.sleep(0.1)
            except Exception as e:
                logger.debug(f"检查 {exchange} {symbol} 时出错: {e}")
                continue
        
        logger.debug(f"{symbol} 最佳交易所: {best_exchange} (成交额: ${max_volume:,.0f})")
        return best_exchange, max_volume
    
    def calculate_price_change(self, current_price: float, symbol: str) -> float:
        """计算1分钟价格变化百分比"""
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        
        # 查找1分钟前的价格
        if symbol in self.historical_data:
            for data_point in reversed(self.historical_data[symbol]):
                if data_point['timestamp'] <= one_minute_ago:
                    old_price = data_point['price']
                    if old_price > 0:
                        return ((current_price - old_price) / old_price) * 100
                    break
        
        return 0.0
    
    def calculate_oi_change(self, current_oi: float, symbol: str) -> float:
        """计算5分钟持仓量变化百分比"""
        now = datetime.now()
        five_minutes_ago = now - timedelta(minutes=5)
        
        # 查找5分钟前的持仓量
        if symbol in self.historical_data:
            for data_point in reversed(self.historical_data[symbol]):
                if data_point['timestamp'] <= five_minutes_ago:
                    old_oi = data_point.get('open_interest', 0)
                    if old_oi > 0:
                        return ((current_oi - old_oi) / old_oi) * 100
                    break
        
        return 0.0
    
    def update_historical_data(self, symbol: str, data: Dict):
        """更新历史数据，保留最近10分钟的数据"""
        if symbol not in self.historical_data:
            self.historical_data[symbol] = []
        
        self.historical_data[symbol].append(data)
        
        # 清理10分钟前的数据
        ten_minutes_ago = datetime.now() - timedelta(minutes=10)
        self.historical_data[symbol] = [
            d for d in self.historical_data[symbol] 
            if d['timestamp'] > ten_minutes_ago
        ]
    
    def get_minute_volume_accurate(self, symbol: str, exchange_name: str) -> float:
        """获取准确的1分钟成交额"""
        try:
            if exchange_name != 'binance':
                return self.get_minute_volume_estimate(symbol, exchange_name)
            
            # 转换符号格式
            binance_symbol = symbol.replace('/', '')
            
            # 获取最新1分钟K线数据
            url = f"{self.binance_spot_base}/klines"
            params = {
                'symbol': binance_symbol,
                'interval': '1m',
                'limit': 1
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    # K线数据：[时间, 开盘, 最高, 最低, 收盘, 成交量, 结束时间, 成交额, ...]
                    return float(data[0][7])  # 成交额
            
            # 降级到估算方法
            return self.get_minute_volume_estimate(symbol, exchange_name)
            
        except Exception as e:
            logger.info(f"获取准确1分钟成交额失败: {e}")
            return self.get_minute_volume_estimate(symbol, exchange_name)
    
    def get_minute_volume_estimate(self, symbol: str, exchange_name: str) -> float:
        """获取1分钟成交额（估算方法）"""
        try:
            spot_data = self.get_spot_data_direct(symbol, exchange_name)
            if spot_data:
                # 将24小时成交额除以1440分钟作为1分钟成交额的估算
                return spot_data['volume_24h'] / 1440
            return 0.0
        except Exception as e:
            logger.info(f"计算1分钟成交额失败: {e}")
            return 0.0
    
    def get_minute_volume(self, symbol: str, exchange_name: str) -> float:
        """获取1分钟成交额（优先使用准确方法）"""
        return self.get_minute_volume_accurate(symbol, exchange_name)
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            # 测试Binance服务器时间
            url = f"{self.binance_spot_base}/time"
            response = self.session.get(url, timeout=5)
            
            if response.status_code == 200:
                logger.info("混合数据采集器连接测试成功")
                return True
            else:
                logger.error(f"连接测试失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

def main():
    """测试混合数据采集器"""
    logging.basicConfig(level=logging.INFO)
    
    print("=== 混合数据采集器测试 ===")
    
    collector = HybridDataCollector()
    
    # 测试连接
    if not collector.test_connection():
        print("ERROR 连接测试失败")
        return
    
    print("OK 连接测试成功")
    
    # 测试获取期货交易对
    symbols = collector.get_binance_futures_symbols_direct()
    print(f"OK 获取到 {len(symbols)} 个期货交易对")
    
    if len(symbols) > 0:
        # 测试获取数据
        test_symbol = symbols[0]
        print(f"\n测试获取 {test_symbol} 数据:")
        
        # 现货数据
        spot_data = collector.get_spot_data_direct(test_symbol, 'binance')
        if spot_data:
            print(f"  现货价格: ${spot_data['price']:,.2f}")
            print(f"  24小时成交额: ${spot_data['volume_24h']:,.0f}")
        
        # 期货数据
        futures_data = collector.get_futures_data_direct(test_symbol)
        if futures_data:
            print(f"  期货价格: ${futures_data['price']:,.2f}")
            print(f"  持仓量: {futures_data['open_interest']:,.0f}")
        
        print("\nOK 混合数据采集器测试完成")
        print("可以用此版本替代原有的DataCollector")
    else:
        print("ERROR 未能获取交易对数据")

if __name__ == "__main__":
    main()