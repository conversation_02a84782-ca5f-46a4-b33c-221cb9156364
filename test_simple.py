#!/usr/bin/env python3
"""
简化的系统测试程序
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_collector import HybridDataCollector
from data_analyzer import DataAnalyzer
from alert_sender import AlertSender

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_data_collector():
    """测试数据采集器"""
    logger.info("测试数据采集器...")
    
    try:
        collector = HybridDataCollector()
        
        # 检查数据采集器初始化
        assert collector.session is not None, "HTTP会话未正确初始化"
        assert collector.binance_spot_base is not None, "Binance现货API端点未初始化"
        assert collector.binance_futures_base is not None, "Binance期货API端点未初始化"
        
        logger.info("OK 数据采集器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"数据采集器测试失败: {e}")
        return False

def test_data_analyzer():
    """测试数据分析器"""
    logger.info("测试数据分析器...")
    
    try:
        analyzer = DataAnalyzer()
        
        # 测试现货告警分析
        symbol = 'BTC/USDT'
        spot_data = {'price': 50000, 'volume_24h': 1000000}
        minute_volume = 60000  
        price_change = 2.5  
        
        triggered, alert_data = analyzer.analyze_spot_alert(
            symbol, 'binance', spot_data, minute_volume, price_change
        )
        
        assert triggered == True, "现货告警应该被触发"
        assert alert_data['type'] == '现货放量', "告警类型不正确"
        
        # 测试期货告警分析
        futures_data = {'price': 50000, 'open_interest': 105000}
        oi_change = 6.0  
        
        triggered, alert_data = analyzer.analyze_futures_alert(
            symbol, futures_data, oi_change
        )
        
        assert triggered == True, "期货告警应该被触发"
        assert alert_data['type'] == '期货加仓', "告警类型不正确"
        
        # 测试消息格式化
        test_alert_data = {
            'type': '现货放量',
            'symbol': 'BTC/USDT',
            'exchange': 'binance',
            'minute_volume': 60000,
            'price_change': 2.5,
            'current_price': 50000,
            'timestamp': datetime.now()
        }
        
        message = analyzer.format_alert_message(test_alert_data)
        assert 'BTC/USDT' in message, "消息应包含交易对"
        assert '现货放量' in message, "消息应包含告警类型"
        
        logger.info("OK 数据分析器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"数据分析器测试失败: {e}")
        return False

def test_alert_sender():
    """测试告警发送器"""
    logger.info("测试告警发送器...")
    
    try:
        sender = AlertSender()
        
        # 测试告警冷却机制
        symbol = 'BTC/USDT'
        alert_type = '现货放量'
        
        result1 = sender.should_send_alert(symbol, alert_type)
        assert result1 == True, "第一次应该允许发送"
        
        result2 = sender.should_send_alert(symbol, alert_type)
        assert result2 == False, "应该被冷却机制阻止"
        
        # 测试清理过期告警
        sender.cleanup_old_alerts()
        
        logger.info("OK 告警发送器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"告警发送器测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    logger.info("测试系统集成...")
    
    try:
        # 初始化所有组件
        collector = HybridDataCollector()
        analyzer = DataAnalyzer()
        sender = AlertSender()
        
        # 模拟完整的工作流程
        symbol = 'BTC/USDT'
        spot_data = {'price': 50000, 'volume_24h': 2000000}
        futures_data = {'price': 50000, 'open_interest': 100000}
        
        # 验证数据质量
        is_valid = analyzer.validate_data_quality(symbol, spot_data, futures_data)
        assert is_valid == True, "数据质量验证应该通过"
        
        # 测试低阈值不触发告警
        minute_volume = 1000  
        price_change = 1.0    
        oi_change = 1.0       
        
        spot_triggered, _ = analyzer.analyze_spot_alert(
            symbol, 'binance', spot_data, minute_volume, price_change
        )
        
        futures_triggered, _ = analyzer.analyze_futures_alert(
            symbol, futures_data, oi_change
        )
        
        assert spot_triggered == False, "现货告警不应被触发"
        assert futures_triggered == False, "期货告警不应被触发"
        
        logger.info("OK 系统集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"系统集成测试失败: {e}")
        return False

def test_performance():
    """性能测试"""
    logger.info("测试系统性能...")
    
    try:
        import time
        
        collector = HybridDataCollector()
        analyzer = DataAnalyzer()
        
        # 模拟处理50个交易对
        symbols = [f'SYMBOL{i}/USDT' for i in range(50)]
        
        start_time = time.time()
        
        for symbol in symbols:
            # 模拟数据处理
            spot_data = {'price': 1000, 'volume_24h': 1000000}
            futures_data = {'price': 1000, 'open_interest': 50000}
            
            # 验证数据
            analyzer.validate_data_quality(symbol, spot_data, futures_data)
            
            # 分析告警
            analyzer.analyze_spot_alert(symbol, 'binance', spot_data, 1000, 0.5)
            analyzer.analyze_futures_alert(symbol, futures_data, 1.0)
        
        end_time = time.time()
        duration = end_time - start_time
        avg_time_per_symbol = duration / len(symbols)
        
        logger.info(f"处理 {len(symbols)} 个交易对耗时 {duration:.2f} 秒")
        logger.info(f"平均每个交易对处理时间: {avg_time_per_symbol*1000:.2f} 毫秒")
        
        # 性能要求：每个交易对处理时间应小于100毫秒
        assert avg_time_per_symbol < 0.1, f"性能要求未达标: {avg_time_per_symbol:.3f}s > 0.1s"
        
        logger.info("OK 性能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=== 开始简化系统测试 ===")
    
    tests = [
        ("数据采集器", test_data_collector),
        ("数据分析器", test_data_analyzer),
        ("告警发送器", test_alert_sender),
        ("系统集成", test_system_integration),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n运行测试: {test_name}")
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            failed += 1
    
    logger.info(f"\n=== 测试总结 ===")
    logger.info(f"通过: {passed}")
    logger.info(f"失败: {failed}")
    logger.info(f"总计: {passed + failed}")
    
    if failed == 0:
        logger.info("OK 所有测试通过，系统可以投入使用！")
        return True
    else:
        logger.error(f"ERROR {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)